import Foundation
import AVFoundation
import AppKit
import ApplicationServices

protocol PermissionsRepository {
    func checkPermissions() async -> PermissionsStatus
    func requestMicrophonePermission() async throws
    func requestAccessibilityPermission() async
    func requestInitialMicrophonePermission() async
    var permissionsPublisher: Published<PermissionsStatus>.Publisher { get }
}

class SystemPermissionsRepository: ObservableObject, PermissionsRepository {
    @Published private var permissions = PermissionsStatus()
    
    var permissionsPublisher: Published<PermissionsStatus>.Publisher {
        $permissions
    }
    
    var currentPermissions: PermissionsStatus {
        permissions
    }
    
    init() {
        Task {
            await updatePermissions()
        }
    }
    
    func checkPermissions() async -> PermissionsStatus {
        await updatePermissions()
        return permissions
    }
    
    func requestMicrophonePermission() async throws {
        let granted = await AVCaptureDevice.requestAccess(for: .audio)
        
        if !granted {
            throw PermissionError.microphoneNotAuthorized
        }
        
        await updatePermissions()
    }
    
    func requestAccessibilityPermission() async {
        await MainActor.run {
            // First, trigger the system prompt if permission is not determined
            let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue(): true] as CFDictionary
            let trusted = AXIsProcessTrustedWithOptions(options)
            
            if !trusted {
                // If the system prompt didn't appear or was dismissed, also open System Preferences
                let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
                NSWorkspace.shared.open(url)
                
                // Show alert explaining what to do
                let alert = NSAlert()
                alert.messageText = "Accessibility Permission Required"
                alert.informativeText = """
                DictateApp needs accessibility permission to enable global hotkeys and direct text insertion.
                
                Please:
                1. Click "Grant Access" in the system dialog (if it appeared)
                2. Or find "DictateApp" in System Settings › Privacy & Security › Accessibility
                3. Enable the checkbox next to DictateApp
                4. Return to the app
                
                If DictateApp is not in the list, use the "+" button to add it.
                """
                alert.addButton(withTitle: "OK")
                alert.runModal()
            }
        }
        
        // Start checking for permission changes
        startPeriodicPermissionCheck()
    }
    
    func requestInitialMicrophonePermission() async {
        let status = AVCaptureDevice.authorizationStatus(for: .audio)
        print("🎤 Initial microphone status: \(status.rawValue)")
        
        if status == .notDetermined {
            print("🎤 Requesting microphone permission...")
            let granted = await AVCaptureDevice.requestAccess(for: .audio)
            print("🎤 Permission granted: \(granted)")
        }
        
        // Always update permissions after checking/requesting  
        await updatePermissions()
    }
    
    private func updatePermissions() async {
        let micStatus = AVCaptureDevice.authorizationStatus(for: .audio) == .authorized
        let accessibilityStatus = await checkAccessibilityPermission()
        
        print("🔄 Updating permissions - Mic: \(micStatus), Accessibility: \(accessibilityStatus)")
        
        await MainActor.run {
            permissions.microphone = micStatus
            permissions.accessibility = accessibilityStatus
            print("📱 App permissions updated - Mic: \(permissions.microphone), Accessibility: \(permissions.accessibility)")
        }
    }
    
    private func checkAccessibilityPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.main.async {
                let trusted = AXIsProcessTrusted()
                print("🔐 PermissionsRepository.checkAccessibilityPermission() = \(trusted)")
                print("🔐 Bundle path: \(Bundle.main.bundlePath)")
                print("🔐 Bundle identifier: \(Bundle.main.bundleIdentifier ?? "unknown")")
                print("🔐 Executable path: \(Bundle.main.executablePath ?? "unknown")")
                print("🔐 Process name: \(ProcessInfo.processInfo.processName)")

                if !trusted {
                    print("🔐 Accessibility permission not granted, trying with prompt...")
                    let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue(): true] as CFDictionary
                    let trustedWithPrompt = AXIsProcessTrustedWithOptions(options)
                    print("🔐 AXIsProcessTrustedWithOptions result: \(trustedWithPrompt)")
                    continuation.resume(returning: trustedWithPrompt)
                } else {
                    print("🔐 Accessibility permission already granted")
                    continuation.resume(returning: trusted)
                }
            }
        }
    }
    
    private func startPeriodicPermissionCheck() {
        // Check permissions every 2 seconds when accessibility is not granted
        Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] timer in
            guard let self = self else {
                timer.invalidate()
                return
            }
            
            Task { @MainActor in
                await self.updatePermissions()
                
                // Stop checking once accessibility is granted
                if self.permissions.accessibility {
                    timer.invalidate()
                }
            }
        }
    }
}