import SwiftUI
import Combine

@MainActor
class MainViewModel: ObservableObject {
    @Published var canRecord: Bool = false
    @Published var showError: Bool = false
    @Published var errorMessage: String = ""
    
    private let appState: AppState
    private let audioRepository: AudioCaptureRepository
    private let transcriptionRepository: TranscriptionRepository
    private let llmRepository: LLMRepository
    private let textOutputRepository: TextOutputRepository
    private let permissionsRepository: PermissionsRepository
    private let hotkeyRepository: HotkeyRepository
    private let keyboardRepository: KeyboardRepository
    
    private var cancellables = Set<AnyCancellable>()
    
    init(
        appState: AppState,
        audioRepository: AudioCaptureRepository,
        transcriptionRepository: TranscriptionRepository,
        llmRepository: LLMRepository,
        textOutputRepository: TextOutputRepository,
        permissionsRepository: PermissionsRepository,
        hotkeyRepository: HotkeyRepository,
        keyboardRepository: KeyboardRepository
    ) {
        self.appState = appState
        self.audioRepository = audioRepository
        self.transcriptionRepository = transcriptionRepository
        self.llmRepository = llmRepository
        self.textOutputRepository = textOutputRepository
        self.permissionsRepository = permissionsRepository
        self.hotkeyRepository = hotkeyRepository
        self.keyboardRepository = keyboardRepository
        
        setupBindings()
        setupAudioFileProcessor()
        setupHotkeyHandling()
        
        Task {
            await permissionsRepository.requestInitialMicrophonePermission()
            await requestInitialAccessibilityPermission()
        }
    }
    
    private func setupBindings() {
        // Reactive binding to permissions repository
        permissionsRepository.permissionsPublisher
            .sink { [weak self] permissions in
                print("🔗 Permissions updated from repository: Mic=\(permissions.microphone), Accessibility=\(permissions.accessibility)")
                self?.appState.updatePermissions(permissions)
            }
            .store(in: &cancellables)

        // Update canRecord based on permissions and recording status
        appState.$permissions
            .combineLatest(appState.$systemStatus)
            .map { permissions, status in
                let result = permissions.microphone && !status.isRecording
                print("🎯 CanRecord updated: \(result) (mic: \(permissions.microphone), recording: \(status.isRecording), accessibility: \(permissions.accessibility))")
                return result
            }
            .assign(to: &$canRecord)

        // Update LLM repository when OpenAI API key changes
        appState.$settings
            .map(\.openAIAPIKey)
            .removeDuplicates()
            .sink { [weak self] apiKey in
                print("🔗 OpenAI API key updated: \(apiKey.isEmpty ? "EMPTY" : "SET (\(apiKey.prefix(10))...)"), updating LLM repository")
                if let llmRepo = self?.llmRepository as? OpenAILLMRepository {
                    llmRepo.updateAPIKey(apiKey)
                    print("🔗 LLM repository updated with new API key")
                } else {
                    print("❌ Could not cast llmRepository to OpenAILLMRepository")
                }
            }
            .store(in: &cancellables)

        // Handle errors
        appState.$systemStatus
            .compactMap { status in
                if case .error(let message) = status {
                    return message
                }
                return nil
            }
            .sink { [weak self] message in
                self?.showError(message)
            }
            .store(in: &cancellables)
    }
    
    private func setupAudioFileProcessor() {
        audioRepository.audioFilePublisher
            .sink { [weak self] audioURL in
                Task {
                    await self?.processAudioFile(audioURL)
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupHotkeyHandling() {
        // Handle hotkey events
        hotkeyRepository.hotkeyPublisher
            .sink { [weak self] event in
                print("🔥 Processing hotkey event: \(event.displayName)")
                self?.handleHotkeyEvent(event)
            }
            .store(in: &cancellables)
        
        // Start monitoring only when accessibility permission is granted
        appState.$permissions
            .sink { [weak self] permissions in
                if permissions.accessibility {
                    print("🔥 Accessibility permission granted, starting hotkey monitoring")
                    self?.hotkeyRepository.startMonitoring()
                } else {
                    print("🔥 Accessibility permission not granted, stopping hotkey monitoring")
                    self?.hotkeyRepository.stopMonitoring()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleHotkeyEvent(_ event: HotkeyEvent) {
        print("🔥🔥🔥 HOTKEY EVENT RECEIVED! Event: \(event.displayName)")
        switch event {
        case .startVAD:
            print("🔥 Handling F7 - Voice Activity Detection")
            startRecording(mode: .voiceActivity)
        case .toggleRecording:
            print("🔥 Handling F8 - Toggle Recording")
            if appState.systemStatus.isRecording {
                stopRecording()
            } else {
                startRecording(mode: .toggle)
            }
        case .startFixedDuration:
            print("🔥 Handling Enter - Fixed Duration")
            startRecording(mode: .fixedDuration)
        case .startContextual:
            print("🔥 Handling F3 - Contextual")
            startRecording(mode: .contextual)
        case .startContextualEdit:
            print("🔥 Handling F4 - Contextual Edit")
            handleContextualEdit()
        case .startEdit:
            print("🔥 Handling F6 - Edit")
            handleEdit()
        }
        print("🔥🔥🔥 HOTKEY EVENT COMPLETE!")
    }
    
    private func handleEdit() {
        // Start recording edit instruction
        startRecording(mode: .edit)
    }
    
    private func handleContextualEdit() {
        // Start recording contextual edit instruction  
        startRecording(mode: .contextualEdit)
    }
    
    func startRecording(mode: RecordingMode) {
        print("🚨🚨🚨 MainViewModel.startRecording CALLED! Mode: \(mode) 🚨🚨🚨")
        Task {
            do {
                print("🎯 Setting app state to listening...")
                appState.setListening(mode: mode)
                print("🎯 Starting audio recording...")
                try await audioRepository.startRecording(mode: mode)
                print("✅ Audio recording started successfully!")
            } catch {
                print("❌ Recording failed: \(error)")
                appState.setError(error.localizedDescription)
            }
        }
    }
    
    func stopRecording() {
        Task {
            await audioRepository.stopRecording()
            appState.setIdle()
        }
    }
    
    func clearHistory() {
        appState.clearHistory()
    }
    
    private func processAudioFile(_ audioURL: URL) async {
        do {
            // Transcribe audio
            appState.setProcessing(step: "Transcribing...")
            let transcription = try await transcriptionRepository.transcribe(audioURL: audioURL)

            print("🔍 RAW TRANSCRIPTION: '\(transcription)'")
            print("🔍 TRANSCRIPTION LENGTH: \(transcription.count)")
            print("🔍 TRANSCRIPTION IS EMPTY: \(transcription.isEmpty)")

            if transcription.isEmpty {
                print("❌ Transcription is empty, setting error and returning")
                appState.setError("No speech detected")
                return
            }

            print("✅ Transcription is not empty, proceeding to process...")

            // Process the transcription based on the current mode
            let finalText = try await processTranscription(transcription)

            print("🔍 FINAL TEXT AFTER PROCESSING: '\(finalText)'")
            print("🔍 FINAL TEXT LENGTH: \(finalText.count)")
            print("🔍 FINAL TEXT IS EMPTY: \(finalText.isEmpty)")

            // Output the text using direct keystroke injection for RDP compatibility
            appState.setProcessing(step: "Typing text...")
            print("🚨🚨🚨 ABOUT TO CALL keyboardRepository.typeText() 🚨🚨🚨")
            print("📝 Final text to type: '\(finalText)'")
            keyboardRepository.typeText(finalText)
            print("✅ keyboardRepository.typeText() call completed")

            // Add to content memory and history
            appState.addToContentMemory(finalText)
            let result = TranscriptionResult(text: finalText)
            appState.addTranscription(result)

            appState.setIdle()

        } catch {
            print("❌ ERROR in processAudioFile: \(error)")
            appState.setError("Processing failed: \(error.localizedDescription)")
        }

        // Clean up the temporary audio file
        try? FileManager.default.removeItem(at: audioURL)
    }
    
    private func processTranscription(_ text: String) async throws -> String {
        guard let currentMode = getCurrentRecordingMode() else {
            return processVoiceCommands(text)
        }
        
        switch currentMode {
        case .contextual:
            // Enhanced transcription with context from content memory
            appState.setProcessing(step: "Enhancing with AI...")
            let context = appState.contentMemory.getContextualHistory(count: appState.settings.f3MemoryCount)
            return try await llmRepository.enhanceTranscription(text, context: context)
            
        case .edit:
            // Edit the last content entry and replace it
            guard let lastEntry = appState.contentMemory.lastEntry() else {
                throw ProcessingError.noTextToEdit
            }
            appState.setProcessing(step: "Processing edit...")
            let context = appState.contentMemory.getContextualHistory(count: appState.settings.contextualMemoryCount, excludeLast: true)
            let editedText = try await llmRepository.editText(lastEntry.text, instruction: text, context: context)
            
            // Replace the previous text with edited version
            keyboardRepository.selectAndReplaceText(originalText: lastEntry.text, newText: editedText)
            appState.addToContentMemory(editedText)
            
            // Return empty string to avoid double typing
            return ""
            
        case .contextualEdit:
            // Contextual edit of the last content entry and replace it
            guard let lastEntry = appState.contentMemory.lastEntry() else {
                throw ProcessingError.noTextToEdit
            }
            appState.setProcessing(step: "Processing contextual edit...")
            let context = appState.contentMemory.getContextualHistory(count: appState.settings.contextualMemoryCount, excludeLast: true)
            let editedText = try await llmRepository.editText(lastEntry.text, instruction: text, context: context)
            
            // Replace the previous text with edited version
            keyboardRepository.selectAndReplaceText(originalText: lastEntry.text, newText: editedText)
            appState.addToContentMemory(editedText)
            
            // Return empty string to avoid double typing
            return ""
            
        default:
            return processVoiceCommands(text)
        }
    }
    
    private func getCurrentRecordingMode() -> RecordingMode? {
        if case .listening(let mode) = appState.systemStatus {
            return mode
        }
        return nil
    }
    
    private func processVoiceCommands(_ text: String) -> String {
        return VoiceCommandProcessor.processCommands(text)
    }
    
    private func requestInitialAccessibilityPermission() async {
        let permissions = await permissionsRepository.checkPermissions()
        if !permissions.accessibility {
            print("🔑 Accessibility permission not granted, requesting...")
            await permissionsRepository.requestAccessibilityPermission()
        } else {
            print("🔑 Accessibility permission already granted")
        }
    }
    
    private func showError(_ message: String) {
        errorMessage = message
        showError = true
        
        // Auto-dismiss error after 5 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            self.showError = false
        }
    }
}

enum ProcessingError: LocalizedError {
    case noTextToEdit
    
    var errorDescription: String? {
        switch self {
        case .noTextToEdit:
            return "No previous text available to edit"
        }
    }
}